using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace ServerManagement
{
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private readonly DispatcherTimer _timer;
        private readonly ObservableCollection<ServerInfo> _servers;

        public MainWindow()
        {
            try
            {
                InitializeComponent();
                DataContext = this;

                _servers = new ObservableCollection<ServerInfo>();
                ServerList.ItemsSource = _servers;

                _timer = new DispatcherTimer();
                _timer.Interval = TimeSpan.FromSeconds(1);
                _timer.Tick += Timer_Tick;
                _timer.Start();

                InitializeServers();
                AddLogEntry("Windows Server Management v2.0 gestartet");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Fehler beim Initialisieren: {ex.Message}", "Fehler", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeServers()
        {
            var defaultServers = new[]
            {
                new ServerInfo { Name = "DC01", Role = "Domain Controller", Status = "Unbekannt", StatusColor = Brushes.Gray },
                new ServerInfo { Name = "DC02", Role = "Domain Controller", Status = "Unbekannt", StatusColor = Brushes.Gray },
                new ServerInfo { Name = "DB01", Role = "Database Server", Status = "Unbekannt", StatusColor = Brushes.Gray },
                new ServerInfo { Name = "APP01", Role = "Application Server", Status = "Unbekannt", StatusColor = Brushes.Gray },
                new ServerInfo { Name = "MAIL", Role = "Mail Server", Status = "Unbekannt", StatusColor = Brushes.Gray },
                new ServerInfo { Name = "FILE", Role = "File Server", Status = "Unbekannt", StatusColor = Brushes.Gray },
                new ServerInfo { Name = "TS01", Role = "Terminal Server", Status = "Unbekannt", StatusColor = Brushes.Gray }
            };

            foreach (var server in defaultServers)
            {
                _servers.Add(server);
            }

            UpdateStatusText($"{_servers.Count} Server geladen");
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await PingAllServersAsync();
        }

        private async void PingAllServers_Click(object sender, RoutedEventArgs e)
        {
            await PingAllServersAsync();
        }

        private async void PingServer_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string serverName)
            {
                await PingServerAsync(serverName);
            }
        }

        private async void RestartServer_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string serverName)
            {
                var result = MessageBox.Show($"Moechten Sie den Server {serverName} wirklich neu starten?", 
                    "Server Neustart", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    await RestartServerAsync(serverName);
                }
            }
        }

        private async void RestartAllServers_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Moechten Sie alle Server in der korrekten Reihenfolge neu starten?\n\nReihenfolge: DC01 -> DC02 -> DB01 -> APP01 -> MAIL -> FILE -> TS01", 
                "Alle Server neu starten", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                await RestartAllServersAsync();
            }
        }

        private void SearchUpdates_Click(object sender, RoutedEventArgs e)
        {
            AddLogEntry("Update-Suche wird implementiert...");
            MessageBox.Show("Die Update-Suche wird in einer zukuenftigen Version implementiert.", 
                "Feature in Entwicklung", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            AddLogEntry("Einstellungen werden geoeffnet...");
            MessageBox.Show("Die Einstellungen werden in einer zukuenftigen Version implementiert.", 
                "Feature in Entwicklung", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async Task PingAllServersAsync()
        {
            ShowProgress("Pinge alle Server...");
            AddLogEntry("Starte Ping-Test fuer alle Server");

            var tasks = _servers.Select(server => PingServerAsync(server.Name)).ToArray();
            await Task.WhenAll(tasks);

            var onlineCount = _servers.Count(s => s.Status.Contains("Online"));
            UpdateStatusText($"{onlineCount}/{_servers.Count} Server online");
            HideProgress();
        }

        private async Task PingServerAsync(string serverName)
        {
            var server = _servers.FirstOrDefault(s => s.Name == serverName);
            if (server == null) return;

            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(serverName, 5000);

                if (reply.Status == IPStatus.Success)
                {
                    server.Status = $"Online ({reply.RoundtripTime}ms)";
                    server.StatusColor = Brushes.Green;
                    AddLogEntry($"Server {serverName}: Online ({reply.RoundtripTime}ms)");
                }
                else
                {
                    server.Status = "Offline";
                    server.StatusColor = Brushes.Red;
                    AddLogEntry($"Server {serverName}: Offline ({reply.Status})");
                }
            }
            catch (Exception ex)
            {
                server.Status = "Fehler";
                server.StatusColor = Brushes.Orange;
                AddLogEntry($"Server {serverName}: Fehler - {ex.Message}");
            }
        }

        private async Task RestartServerAsync(string serverName)
        {
            ShowProgress($"Starte {serverName} neu...");
            AddLogEntry($"Neustart von Server {serverName} wird simuliert...");

            try
            {
                // Simulate restart process
                await Task.Delay(2000);
                
                AddLogEntry($"Server {serverName}: Neustart-Befehl gesendet");
                await Task.Delay(1000);
                
                AddLogEntry($"Server {serverName}: Warte auf Neustart...");
                await Task.Delay(3000);
                
                // Ping after restart
                await PingServerAsync(serverName);
                
                AddLogEntry($"Server {serverName}: Neustart abgeschlossen");
            }
            catch (Exception ex)
            {
                AddLogEntry($"Fehler beim Neustart von {serverName}: {ex.Message}");
            }
            finally
            {
                HideProgress();
            }
        }

        private async Task RestartAllServersAsync()
        {
            ShowProgress("Starte alle Server neu...");
            
            var serverOrder = new[] { "DC01", "DC02", "DB01", "APP01", "MAIL", "FILE", "TS01" };
            
            foreach (var serverName in serverOrder)
            {
                AddLogEntry($"Starte {serverName} neu...");
                await RestartServerAsync(serverName);
                
                if (serverName != "TS01") // No delay after last server
                {
                    AddLogEntry($"Warte 30 Sekunden vor dem naechsten Server...");
                    await Task.Delay(3000); // Shortened for demo
                }
            }
            
            AddLogEntry("Alle Server wurden neu gestartet");
            HideProgress();
        }

        private void ShowProgress(string message)
        {
            StatusText.Text = message;
            ProgressBar.Visibility = Visibility.Visible;
        }

        private void HideProgress()
        {
            ProgressBar.Visibility = Visibility.Collapsed;
            UpdateStatusText("Bereit");
        }

        private void UpdateStatusText(string text)
        {
            StatusText.Text = text;
        }

        private void AddLogEntry(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var logEntry = $"[{timestamp}] {message}\n";
            
            Dispatcher.Invoke(() =>
            {
                LogTextBlock.Text += logEntry;
                
                // Keep only last 50 lines
                var lines = LogTextBlock.Text.Split('\n');
                if (lines.Length > 50)
                {
                    LogTextBlock.Text = string.Join("\n", lines.Skip(lines.Length - 50));
                }
            });
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ServerInfo : INotifyPropertyChanged
    {
        private string _status;
        private Brush _statusColor;

        public string Name { get; set; }
        public string Role { get; set; }
        
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }
        
        public Brush StatusColor
        {
            get => _statusColor;
            set
            {
                _statusColor = value;
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
